# K8s SRE 智能助手

基于 LangGraph 的 Kubernetes SRE 智能助手，支持 OOM 问题自动排查与修复、集群健康巡检与风险评估。

## 🌟 核心特性

### 🔧 OOM 问题自动排查
- **智能诊断**：自动收集 K8s 事件、SLS 日志、ARMS 监控数据
- **根因分析**：基于多维度证据进行智能分析
- **自动修复**：支持资源限制调整、节点池扩容等操作
- **闭环验证**：执行后自动验证修复效果
- **人工确认**：关键操作前支持人工审核

### 🏥 集群健康巡检
- **全面检查**：覆盖节点、Pod、服务等核心资源
- **异常挖掘**：智能分析日志中的异常模式
- **性能监控**：评估集群资源使用和性能指标
- **风险评估**：识别潜在问题并提供优化建议
- **专业报告**：生成结构化的健康巡检报告

## 🏛️ 架构设计

### 核心组件
```
src/
├── models/          # 数据模型定义
│   └── state.py     # GraphState 和 Pydantic 模型
├── tools/           # 工具注册表
│   └── registry.py  # 与外部系统交互的工具
├── agents/          # 智能体节点
│   ├── common_agents.py      # 通用节点
│   ├── oom_agents.py         # OOM 排查节点
│   └── health_check_agents.py # 健康巡检节点
├── workflows/       # 工作流定义
│   ├── oom_workflow.py       # OOM 排查工作流
│   └── health_check_workflow.py # 健康巡检工作流
├── llm/            # LLM 提供者
│   └── provider.py  # LLM 调用封装
└── main.py         # 主应用入口
```

### 技术栈
- **LangGraph**: 工作流编排框架
- **Pydantic**: 数据校验和建模
- **asyncio**: 异步编程支持
- **tenacity**: 重试机制
- **httpx**: HTTP 客户端

## 🚀 快速开始

### 环境要求
- Python 3.10+
- uv (推荐的包管理器)

### 安装依赖
```bash
# 进入项目目录
cd src-code

# 安装依赖
uv sync
```

### 配置环境变量
```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑配置文件，填入实际的 API 密钥和服务器地址
vim .env
```

#### 主要配置项说明

**LLM 配置**
- `USE_QWEN_LLM`: 是否使用 Qwen 模型 (true/false)
- `DASHSCOPE_API_KEY`: 阿里云百炼 API 密钥
- `QWEN_MODEL`: 使用的 Qwen 模型名称

**MCP 工具配置**
- `USE_MCP_TOOLS`: 是否使用 MCP 工具 (true/false)
- `MCP_SERVER_URL`: MCP 服务器地址 (支持 SSE 协议)
- `MCP_SERVER_AUTH_TOKEN`: MCP 服务器认证令牌

### 运行程序

#### 🤖 多智能体版本（推荐）
基于 LangGraph 最佳实践的现代化多智能体架构：

```bash
# 交互模式 - 支持多轮对话
python main_multi_agent.py

# 单问题模式
python main_multi_agent.py -q "检查集群健康状态"

# 批处理模式
python main_multi_agent.py --batch "检查 OOM 问题" "扩容 deployment" "分析日志"

# 详细输出模式
python main_multi_agent.py -v
```

**多智能体系统特性**：
- 🧠 **专门智能体**：OOM诊断、健康检查、K8s操作、日志分析、监控分析
- 👨‍💼 **智能协调**：SRE主管智能体自动分配任务给最合适的专家
- 🔄 **智能协作**：智能体间可以协作处理复杂问题
- 💬 **多轮对话**：支持基于上下文的连续对话

#### 📋 传统工作流程版本
```bash
# 运行传统演示
python main.py
```

## 📖 使用指南

### 1. 集群健康巡检

```python
from src.main import K8sSREAgent

# 创建智能助手实例
agent = K8sSREAgent()

# 运行健康巡检
result = await agent.run_health_check(
    namespace="default",
    sls_project="your-sls-project",
    sls_logstore="your-logstore",
    arms_project="your-arms-project",
    arms_metricstore="your-metricstore",
    region_id="cn-hangzhou"
)

# 打印报告
agent.print_health_report(result)
```

### 2. OOM 问题排查

```python
# 运行 OOM 排查（带人工确认）
result = await agent.run_oom_troubleshooting(
    namespace="default",
    sls_project="your-sls-project",
    sls_logstore="your-logstore",
    arms_project="your-arms-project",
    arms_metricstore="your-metricstore",
    region_id="cn-hangzhou",
    cluster_id="your-cluster-id",
    nodepool_id="your-nodepool-id",
    problem_description="发现多个 Pod 出现 OOMKilled 状态",
    enable_human_confirmation=True
)

# 打印报告
agent.print_oom_report(result)
```

## 🔧 配置说明

### 必需参数
- `sls_project`: SLS 日志项目名称
- `sls_logstore`: SLS 日志库名
- `arms_project`: ARMS/CMS Project 名称
- `arms_metricstore`: Prometheus 指标存储名称
- `region_id`: 云区域 ID

### OOM 排查额外参数
- `cluster_id`: 集群 ID（用于节点池扩容）
- `nodepool_id`: 节点池 ID（用于节点池扩容）

### 可选参数
- `namespace`: Kubernetes 命名空间（默认: "default"）
- `problem_description`: 问题描述
- `enable_human_confirmation`: 是否启用人工确认（默认: true）

## 🔄 工作流程

### OOM 排查工作流
1. **并行证据收集**：同时收集 K8s、SLS、ARMS 数据
2. **综合分析**：基于所有证据进行根因分析
3. **方案制定**：生成具体的修复方案
4. **人工确认**：等待用户确认执行方案
5. **执行修复**：自动执行批准的操作
6. **验证结果**：检查问题是否解决
7. **生成报告**：输出详细的执行报告

### 健康巡检工作流
1. **资源检查**：全面检查 K8s 核心资源状态
2. **日志分析**：挖掘 SLS 日志中的异常模式
3. **指标监控**：分析 ARMS 性能指标
4. **风险评估**：识别潜在问题和风险点
5. **报告生成**：输出结构化健康报告

## 🛠️ 扩展开发

### 添加新的工具
1. 在 `src/tools/registry.py` 中注册新工具
2. 实现工具的调用逻辑
3. 在相应的 Agent 中使用新工具

### 添加新的工作流
1. 在 `src/workflows/` 中创建新的工作流文件
2. 定义工作流的节点和边
3. 在主应用中集成新工作流

### 自定义 LLM 提供者
1. 继承 `LLMProvider` 基类
2. 实现 `ainvoke` 和 `ainvoke_structured` 方法
3. 在 `src/llm/provider.py` 中替换默认提供者

## 📊 监控和日志

### 日志配置
- 日志级别：INFO
- 输出：控制台 + 文件 (`k8s_sre_agent.log`)
- 格式：时间戳 + 模块名 + 级别 + 消息

### 关键指标
- 工作流执行时间
- 工具调用成功率
- LLM 调用次数
- 问题解决率

## 🔒 安全考虑

### 权限控制
- 确保 K8s 集群访问权限最小化
- SLS 和 ARMS 访问权限按需分配
- 敏感操作需要人工确认

### 数据安全
- 不在日志中记录敏感信息
- 使用环境变量管理 API 密钥
- 定期轮换访问凭证

## 🎯 设计原则

本项目严格遵循以下设计原则：

### 1. 诊断 → 方案 → 执行 → 验证 闭环原则
- **OOM 排查**：完整的闭环流程，确保问题真正解决
- **健康巡检**：专注于诊断和报告，提供决策支持

### 2. 模块化与可扩展性
- **Agent 节点**：独立的功能模块，易于测试和维护
- **工作流编排**：基于 LangGraph 的灵活编排
- **工具注册**：统一的工具管理和调用接口

### 3. 类型安全与数据校验
- **Pydantic 模型**：运行时数据校验
- **TypedDict**：编译时类型检查
- **严格类型注解**：提高代码质量

### 4. 错误处理与重试机制
- **节点级错误捕获**：防止单点故障
- **工作流级重试**：智能重试机制
- **优雅降级**：部分失败时仍能提供有价值的信息

## 🧪 测试

### 多智能体系统测试
```bash
# 运行多智能体系统测试
python test_multi_agent.py

# 运行集成测试
python test_integration.py
```

### 测试覆盖
- ✅ 智能体功能测试
- ✅ 智能体协作测试
- ✅ 工具集成测试
- ✅ LLM Provider 测试
- ✅ 配置管理测试

## 🔄 架构演进

### v1.0 - 传统工作流程
- 基于 LangGraph 的节点工作流
- 固定的执行路径
- 单一智能体处理

### v2.0 - 多智能体架构（当前）
- 基于 `create_react_agent` 的专门智能体
- Supervisor 架构协调
- 智能体间 handoff 机制
- 支持多轮对话和中断

### 未来规划
- 🔮 动态智能体创建
- 🔮 更复杂的协作模式
- 🔮 学习和优化能力
- 🔮 更多领域专家智能体

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [LangGraph](https://github.com/langchain-ai/langgraph) - 工作流编排框架
- [Pydantic](https://github.com/pydantic/pydantic) - 数据验证库
- [阿里云](https://www.aliyun.com/) - SLS 和 ARMS 服务提供商