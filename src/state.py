"""
State management for the K8s SRE multi-agent system.

Defines the shared state structure used across all agents in the LangGraph workflow.
"""

from typing import TypedDict, Annotated, List, Optional
from langgraph.graph.message import AnyMessage, add_messages


class SREMultiAgentState(TypedDict):
    """
    Shared state for the SRE multi-agent system.
    
    This state is passed between all agents and contains:
    - messages: Conversation history with automatic message aggregation
    - task: Current task description
    - supervisor_report: Final report from supervisor
    - last_active_agent: Track which agent was last active
    - current_task_status: Status of current task (pending, in_progress, completed, failed)
    """
    messages: Annotated[List[AnyMessage], add_messages]
    task: str
    supervisor_report: str
    last_active_agent: Optional[str]
    current_task_status: Optional[str]
