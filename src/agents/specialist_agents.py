"""
简化的专家智能体实现
"""

import os
from langchain_core.tools import BaseTool
from langgraph.prebuilt import create_react_agent
from dotenv import load_dotenv

from ..llm import LLM
from ..prompts import K8S_DIAG_PROMPT, SLS_DIAG_PROMPT, ARMS_DIAG_PROMPT, K8S_OPS_PROMPT

load_dotenv()


class MockTool(BaseTool):
    """模拟工具"""
    name: str
    description: str

    def _run(self, *args, **kwargs) -> str:
        return f"模拟工具 {self.name} 的响应"


def get_mcp_tools():
    """获取 MCP 工具，如果不可用则使用模拟工具"""
    # 简化：直接返回模拟工具
    return [
        MockTool(name="kubectl_get_pods", description="获取 Pod 状态"),
        MockTool(name="kubectl_describe_pod", description="获取 Pod 详细信息"),
        MockT<PERSON>(name="query_sls_logs", description="查询 SLS 日志"),
        MockT<PERSON>(name="query_arms_metrics", description="查询 ARMS 指标"),
        MockT<PERSON>(name="kubectl_patch_deployment_resources",
                 description="调整资源配置"),
        MockTool(name="cs_autoscaler_scale_nodepool", description="扩容节点池"),
    ]


ALL_TOOLS = get_mcp_tools()
TOOL_MAP = {tool.name: tool for tool in ALL_TOOLS}

# 工具分配
k8s_diag_tools = [TOOL_MAP["kubectl_get_pods"],
                  TOOL_MAP["kubectl_describe_pod"]]
sls_diag_tools = [TOOL_MAP["query_sls_logs"]]
arms_diag_tools = [TOOL_MAP["query_arms_metrics"]]
k8s_ops_tools = [TOOL_MAP["kubectl_patch_deployment_resources"],
                 TOOL_MAP["cs_autoscaler_scale_nodepool"]]

# 创建智能体
k8s_diagnostic_agent = create_react_agent(
    LLM, k8s_diag_tools, state_modifier=K8S_DIAG_PROMPT)
sls_diagnostic_agent = create_react_agent(
    LLM, sls_diag_tools, state_modifier=SLS_DIAG_PROMPT)
arms_diagnostic_agent = create_react_agent(
    LLM, arms_diag_tools, state_modifier=ARMS_DIAG_PROMPT)
k8s_operations_agent = create_react_agent(
    LLM, k8s_ops_tools, state_modifier=K8S_OPS_PROMPT)
