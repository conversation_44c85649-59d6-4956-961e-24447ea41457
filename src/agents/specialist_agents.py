"""
Specialist agent implementations for the K8s SRE multi-agent system.

This module creates specialized agents for different aspects of SRE work:
- K8s diagnostic agent for cluster troubleshooting
- SLS diagnostic agent for log analysis  
- ARMS diagnostic agent for monitoring analysis
- K8s operations agent for cluster operations
"""

import os
import logging
from typing import List, Dict, Any, Optional
from langchain_core.tools import BaseTool
from langgraph.prebuilt import create_react_agent
from dotenv import load_dotenv

from ..llm import LLM
from ..prompts import K8S_DIAG_PROMPT, SLS_DIAG_PROMPT, ARMS_DIAG_PROMPT, K8S_OPS_PROMPT

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)


class MockTool(BaseTool):
    """Mock tool for development and testing when MCP servers are not available."""
    
    name: str
    description: str
    
    def _run(self, *args, **kwargs) -> str:
        return f"Mock response from {self.name}. In production, this would connect to actual MCP server."


def get_mcp_tools() -> List[BaseTool]:
    """
    Get tools from MCP servers.
    
    This function attempts to connect to MCP servers and retrieve available tools.
    If MCP servers are not available, it returns mock tools for development.
    
    Returns:
        List of available tools from MCP servers
    """
    use_mcp = os.getenv("USE_MCP_TOOLS", "false").lower() == "true"
    
    if not use_mcp:
        logger.info("MCP tools disabled, using mock tools for development")
        return _get_mock_tools()
    
    try:
        # Try to import and use MCP adapters
        from langchain_mcp_adapters.client import MCPClient
        
        mcp_url = os.getenv("MCP_SERVER_URL")
        if not mcp_url:
            logger.warning("MCP_SERVER_URL not set, falling back to mock tools")
            return _get_mock_tools()
        
        # Create MCP client
        client = MCPClient(mcp_url)
        tools = client.get_tools()
        
        logger.info(f"Successfully loaded {len(tools)} tools from MCP server")
        return tools
        
    except ImportError:
        logger.warning("langchain-mcp-adapters not available, using mock tools")
        return _get_mock_tools()
    except Exception as e:
        logger.error(f"Failed to connect to MCP server: {e}")
        logger.info("Falling back to mock tools")
        return _get_mock_tools()


def _get_mock_tools() -> List[BaseTool]:
    """Create mock tools for development and testing."""
    
    mock_tools = [
        # K8s diagnostic tools
        MockTool(
            name="kubectl_get_pods",
            description="Get Kubernetes pods status and information"
        ),
        MockTool(
            name="kubectl_describe_pod", 
            description="Get detailed information about a specific Kubernetes pod"
        ),
        MockTool(
            name="kubectl_get_nodes",
            description="Get Kubernetes nodes status and information"
        ),
        MockTool(
            name="kubectl_describe_node",
            description="Get detailed information about a specific Kubernetes node"
        ),
        
        # SLS diagnostic tools
        MockTool(
            name="query_sls_logs",
            description="Query SLS (Simple Log Service) logs with custom queries"
        ),
        MockTool(
            name="search_error_logs",
            description="Search for error-related logs in SLS"
        ),
        MockTool(
            name="search_oom_logs", 
            description="Search for OOM (Out of Memory) related logs"
        ),
        
        # ARMS diagnostic tools
        MockTool(
            name="query_arms_metrics",
            description="Query ARMS/Prometheus monitoring metrics"
        ),
        MockTool(
            name="get_cpu_usage",
            description="Get CPU usage metrics from monitoring system"
        ),
        MockTool(
            name="get_memory_usage",
            description="Get memory usage metrics from monitoring system"
        ),
        
        # K8s operations tools
        MockTool(
            name="kubectl_patch_deployment_resources",
            description="Patch Kubernetes deployment resource limits and requests"
        ),
        MockTool(
            name="cs_autoscaler_scale_nodepool",
            description="Scale Kubernetes node pool using cluster autoscaler"
        ),
        MockTool(
            name="scale_deployment",
            description="Scale Kubernetes deployment replicas"
        ),
        MockTool(
            name="restart_deployment",
            description="Restart Kubernetes deployment"
        ),
    ]
    
    return mock_tools


# Get all available tools
ALL_TOOLS: List[BaseTool] = get_mcp_tools()
TOOL_MAP: Dict[str, BaseTool] = {tool.name: tool for tool in ALL_TOOLS}

# Tool distribution for different agents
k8s_diag_tools = [
    tool for tool in ALL_TOOLS 
    if tool.name in ["kubectl_get_pods", "kubectl_describe_pod", "kubectl_get_nodes", "kubectl_describe_node"]
]

sls_diag_tools = [
    tool for tool in ALL_TOOLS
    if tool.name in ["query_sls_logs", "search_error_logs", "search_oom_logs"]
]

arms_diag_tools = [
    tool for tool in ALL_TOOLS
    if tool.name in ["query_arms_metrics", "get_cpu_usage", "get_memory_usage"]
]

k8s_ops_tools = [
    tool for tool in ALL_TOOLS
    if tool.name in ["kubectl_patch_deployment_resources", "cs_autoscaler_scale_nodepool", 
                     "scale_deployment", "restart_deployment"]
]

# Create specialist agents
logger.info("Creating specialist agents...")

k8s_diagnostic_agent = create_react_agent(
    LLM, 
    k8s_diag_tools, 
    state_modifier=K8S_DIAG_PROMPT
)

sls_diagnostic_agent = create_react_agent(
    LLM,
    sls_diag_tools,
    state_modifier=SLS_DIAG_PROMPT
)

arms_diagnostic_agent = create_react_agent(
    LLM,
    arms_diag_tools, 
    state_modifier=ARMS_DIAG_PROMPT
)

k8s_operations_agent = create_react_agent(
    LLM,
    k8s_ops_tools,
    state_modifier=K8S_OPS_PROMPT
)

logger.info("All specialist agents created successfully")
