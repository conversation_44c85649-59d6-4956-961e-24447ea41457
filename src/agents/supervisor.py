"""
Supervisor agent implementation for the K8s SRE multi-agent system.

The supervisor agent coordinates and manages the specialist agents,
making decisions about task routing and result integration.
"""

import logging
from typing import Literal, Dict, Any
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.graph import MessagesState

from ..llm import LLM
from ..prompts import SUPERVISOR_PROMPT
from .specialist_agents import (
    k8s_diagnostic_agent,
    sls_diagnostic_agent, 
    arms_diagnostic_agent,
    k8s_operations_agent
)

# Configure logging
logger = logging.getLogger(__name__)

# Define available team members
members = [
    "k8s_diagnostic_agent",
    "sls_diagnostic_agent", 
    "arms_diagnostic_agent",
    "k8s_operations_agent"
]

# Agent mapping for execution
AGENT_MAP = {
    "k8s_diagnostic_agent": k8s_diagnostic_agent,
    "sls_diagnostic_agent": sls_diagnostic_agent,
    "arms_diagnostic_agent": arms_diagnostic_agent, 
    "k8s_operations_agent": k8s_operations_agent
}


def sre_supervisor_agent(state: MessagesState) -> Dict[str, Any]:
    """
    SRE Supervisor agent that analyzes problems and routes tasks to appropriate specialists.
    
    Args:
        state: Current conversation state
        
    Returns:
        Updated state with supervisor's decision
    """
    logger.info("SRE Supervisor analyzing the situation...")
    
    # Get the latest message
    messages = state.get("messages", [])
    if not messages:
        return {
            "messages": [SystemMessage(content="No messages to process")],
            "next": "FINISH"
        }
    
    latest_message = messages[-1]
    
    # Create supervisor prompt with context
    supervisor_context = f"""
    {SUPERVISOR_PROMPT}
    
    Available team members: {', '.join(members)}
    
    Current situation: {latest_message.content if hasattr(latest_message, 'content') else str(latest_message)}
    
    Please analyze the situation and decide:
    1. Which specialist(s) should handle this task?
    2. What specific instructions should be given?
    3. Should this be handled sequentially or in parallel?
    
    Respond with your analysis and routing decision.
    """
    
    try:
        # Get supervisor's decision
        response = LLM.invoke([SystemMessage(content=supervisor_context)])
        
        # Analyze the response to determine next action
        response_content = response if isinstance(response, str) else str(response)
        next_agent = _determine_next_agent(response_content, latest_message.content if hasattr(latest_message, 'content') else str(latest_message))
        
        logger.info(f"Supervisor decided to route to: {next_agent}")
        
        return {
            "messages": [SystemMessage(content=f"Supervisor Analysis: {response_content}")],
            "next": next_agent
        }
        
    except Exception as e:
        logger.error(f"Error in supervisor agent: {e}")
        return {
            "messages": [SystemMessage(content=f"Supervisor error: {str(e)}")],
            "next": "FINISH"
        }


def _determine_next_agent(supervisor_response: str, user_input: str) -> str:
    """
    Determine which agent should handle the task based on supervisor analysis and user input.
    
    Args:
        supervisor_response: The supervisor's analysis
        user_input: Original user input
        
    Returns:
        Name of the next agent to route to
    """
    # Convert to lowercase for easier matching
    response_lower = supervisor_response.lower()
    input_lower = user_input.lower()
    
    # Check for operation-related keywords (highest priority - needs human approval)
    operation_keywords = ["scale", "restart", "patch", "deploy", "delete", "modify", "change", "fix", "execute"]
    if any(keyword in response_lower or keyword in input_lower for keyword in operation_keywords):
        if "k8s_operations_agent" in response_lower:
            return "k8s_operations_agent"
    
    # Check for OOM-related issues (multi-agent approach)
    oom_keywords = ["oom", "out of memory", "memory", "killed", "evicted"]
    if any(keyword in input_lower for keyword in oom_keywords):
        # For OOM issues, start with k8s diagnostic
        return "k8s_diagnostic_agent"
    
    # Check for log analysis needs
    log_keywords = ["log", "error", "exception", "trace", "debug"]
    if any(keyword in input_lower for keyword in log_keywords):
        return "sls_diagnostic_agent"
    
    # Check for monitoring/metrics needs
    monitoring_keywords = ["metric", "monitor", "performance", "cpu", "memory usage", "alert"]
    if any(keyword in input_lower for keyword in monitoring_keywords):
        return "arms_diagnostic_agent"
    
    # Check for k8s-specific issues
    k8s_keywords = ["pod", "node", "deployment", "service", "namespace", "cluster"]
    if any(keyword in input_lower for keyword in k8s_keywords):
        return "k8s_diagnostic_agent"
    
    # Check supervisor's explicit recommendation
    for agent in members:
        if agent in response_lower:
            return agent
    
    # Default to k8s diagnostic for general issues
    return "k8s_diagnostic_agent"


def call_specialist_agent(agent_name: str, state: MessagesState) -> Dict[str, Any]:
    """
    Call a specific specialist agent.
    
    Args:
        agent_name: Name of the agent to call
        state: Current state
        
    Returns:
        Updated state with agent's response
    """
    if agent_name not in AGENT_MAP:
        logger.error(f"Unknown agent: {agent_name}")
        return {
            "messages": [SystemMessage(content=f"Error: Unknown agent {agent_name}")],
            "next": "supervisor"
        }
    
    try:
        logger.info(f"Calling specialist agent: {agent_name}")
        agent = AGENT_MAP[agent_name]
        
        # Call the specialist agent
        result = agent.invoke(state)
        
        # Update state to return to supervisor
        if isinstance(result, dict) and "messages" in result:
            result["next"] = "supervisor"
            result["last_active_agent"] = agent_name
        
        return result
        
    except Exception as e:
        logger.error(f"Error calling agent {agent_name}: {e}")
        return {
            "messages": [SystemMessage(content=f"Error in {agent_name}: {str(e)}")],
            "next": "supervisor"
        }


# Convenience functions for each specialist agent
def call_k8s_diagnostic_agent(state: MessagesState) -> Dict[str, Any]:
    """Call the K8s diagnostic agent."""
    return call_specialist_agent("k8s_diagnostic_agent", state)


def call_sls_diagnostic_agent(state: MessagesState) -> Dict[str, Any]:
    """Call the SLS diagnostic agent.""" 
    return call_specialist_agent("sls_diagnostic_agent", state)


def call_arms_diagnostic_agent(state: MessagesState) -> Dict[str, Any]:
    """Call the ARMS diagnostic agent."""
    return call_specialist_agent("arms_diagnostic_agent", state)


def call_k8s_operations_agent(state: MessagesState) -> Dict[str, Any]:
    """Call the K8s operations agent."""
    return call_specialist_agent("k8s_operations_agent", state)
