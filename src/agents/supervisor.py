"""
简化的 Supervisor Agent 实现
"""

from typing import Literal
from langchain_core.messages import HumanMessage
from ..llm import LLM
from ..prompts import SUPERVISOR_PROMPT
from .specialist_agents import (
    k8s_diagnostic_agent,
    sls_diagnostic_agent,
    arms_diagnostic_agent,
    k8s_operations_agent
)

# 团队成员列表
members = ["k8s_diagnostic_agent", "sls_diagnostic_agent",
           "arms_diagnostic_agent", "k8s_operations_agent"]


def sre_supervisor_agent(state):
    """SRE 主管智能体 - 负责任务分析和路由"""
    messages = state["messages"]

    # 构建系统提示
    system_message = f"{SUPERVISOR_PROMPT}\n\n可用团队成员: {members}"

    # 调用 LLM 进行决策
    response = LLM.invoke([
        {"role": "system", "content": system_message},
        {"role": "user", "content": messages[-1].content}
    ])

    return {"messages": [HumanMessage(content=response)]}
