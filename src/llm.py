"""
LLM configuration for the K8s SRE multi-agent system.

Configures Qwen model via OpenAI-compatible API from DashScope.
"""

import os
from typing import Optional
from langchain_core.language_models.chat_models import BaseChatModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def get_llm(
    model: str = "qwen-plus",
    temperature: float = 0.0,
    streaming: bool = True,
    max_tokens: Optional[int] = None
) -> BaseChatModel:
    """
    Create and configure the LLM instance.
    
    Args:
        model: Model name (default: qwen-plus)
        temperature: Sampling temperature (default: 0.0 for deterministic responses)
        streaming: Enable streaming responses (default: True)
        max_tokens: Maximum tokens in response (default: None)
        
    Returns:
        Configured LLM instance
        
    Raises:
        ValueError: If QWEN_API_KEY is not set
    """
    api_key = os.getenv("QWEN_API_KEY")
    if not api_key:
        raise ValueError(
            "QWEN_API_KEY is not set in the environment variables. "
            "Please set it in your .env file."
        )

    try:
        # Try to import OpenAI-compatible client
        from openai import OpenAI
        
        # Create OpenAI client configured for DashScope
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
        # Create a simple wrapper for LangChain compatibility
        class QwenLLM:
            def __init__(self, client, model, temperature, max_tokens):
                self.client = client
                self.model = model
                self.temperature = temperature
                self.max_tokens = max_tokens
                
            def invoke(self, messages):
                # Convert LangChain messages to OpenAI format
                openai_messages = []
                for msg in messages:
                    if hasattr(msg, 'content') and hasattr(msg, 'type'):
                        role = "user" if msg.type == "human" else "assistant"
                        openai_messages.append({"role": role, "content": msg.content})
                    elif isinstance(msg, tuple):
                        role, content = msg
                        role = "user" if role == "human" else "assistant"
                        openai_messages.append({"role": role, "content": content})
                
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=openai_messages,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
                
                return response.choices[0].message.content
        
        return QwenLLM(client, model, temperature, max_tokens)
        
    except ImportError:
        # Fallback to langchain-openai if available
        try:
            from langchain_openai import ChatOpenAI
            
            llm = ChatOpenAI(
                model=model,
                openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
                openai_api_key=api_key,
                temperature=temperature,
                streaming=streaming,
                max_tokens=max_tokens,
            )
            return llm
            
        except ImportError:
            raise ImportError(
                "Neither 'openai' nor 'langchain-openai' package is available. "
                "Please install one of them: pip install openai or pip install langchain-openai"
            )


# Create default LLM instance
LLM = get_llm()
