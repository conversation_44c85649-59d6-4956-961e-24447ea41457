# K8s SRE 多智能体架构详解

本文档详细介绍基于 LangGraph 最佳实践设计的多智能体架构。

## 🏗️ 架构概览

### Supervisor 架构模式

我们采用 **Supervisor** 架构模式，这是 LangGraph 推荐的多智能体架构之一：

```
                    👨‍💼 SRE Supervisor
                           |
                    (任务分析和分配)
                           |
        ┌─────────┬─────────┼─────────┬─────────┐
        │         │         │         │         │
       🧠        💚        ⚙️        📋        📊
    OOM诊断    健康检查   K8s操作   日志分析   监控分析
     专家       专家       专家       专家       专家
```

### 核心优势

1. **专业化分工**：每个智能体专注于特定领域，提供专业的解决方案
2. **智能协调**：Supervisor 智能体负责理解需求并分配给最合适的专家
3. **灵活协作**：智能体间可以相互协作处理复杂问题
4. **可扩展性**：易于添加新的专门智能体

## 🤖 智能体详解

### 👨‍💼 SRE Supervisor（主管智能体）

**核心职责**：
- 理解和分析用户问题
- 判断问题类型和复杂度
- 选择最合适的专门智能体
- 协调多智能体协作
- 整合和汇总结果

**决策逻辑**：
```python
问题类型映射 = {
    "内存相关": "oom_diagnostic_agent",
    "健康检查": "health_check_agent", 
    "操作执行": "k8s_operations_agent",
    "日志分析": "log_analysis_agent",
    "监控数据": "monitoring_agent"
}
```

**工具集**：
- `assign_task_to_*`: 分配具体任务给专门智能体
- `transfer_to_*`: 简单转交控制权

### 🧠 OOM Diagnostic Agent（OOM诊断专家）

**专业领域**：内存溢出问题诊断和分析

**核心能力**：
- 内存使用模式分析
- OOM 事件根因分析
- 内存配置优化建议
- 内存泄漏检测

**工具集**：
- `kubectl_get_pods`: 获取 Pod 状态
- `kubectl_describe_pod`: 获取 Pod 详细信息
- `search_oom_logs`: 搜索 OOM 相关日志
- `get_memory_usage`: 获取内存使用监控数据

**典型工作流程**：
1. 获取相关 Pod 的当前状态
2. 查询历史内存使用数据
3. 搜索 OOM 相关日志事件
4. 分析内存配置和限制
5. 提供诊断结论和优化建议

### 💚 Health Check Agent（健康检查专家）

**专业领域**：集群和应用健康状态评估

**核心能力**：
- 全面的集群健康评估
- 应用运行状态分析
- 资源使用趋势监控
- 风险识别和预警

**工具集**：
- `kubectl_get_nodes`: 获取节点状态
- `kubectl_get_pods`: 获取 Pod 状态
- `get_cpu_usage`: 获取 CPU 使用情况
- `get_memory_usage`: 获取内存使用情况
- `search_error_logs`: 搜索错误日志

**健康检查维度**：
- 节点健康状态
- Pod 运行状态
- 资源使用率
- 错误日志频率
- 关键服务可用性

### ⚙️ K8s Operations Agent（K8s操作专家）

**专业领域**：Kubernetes 集群操作执行

**核心能力**：
- 安全的集群操作执行
- 操作前状态验证
- 操作后结果确认
- 回滚建议提供

**工具集**：
- `scale_deployment`: Deployment 扩缩容
- `restart_deployment`: Deployment 重启
- `scale_nodepool`: 节点池扩缩容
- `kubectl_describe_pod`: 操作前状态检查

**安全原则**：
- 操作前必须确认当前状态
- 重要操作需要明确确认
- 详细记录所有操作
- 提供回滚方案

### 📋 Log Analysis Agent（日志分析专家）

**专业领域**：日志查询、分析和问题定位

**核心能力**：
- 精确的日志查询构建
- 日志模式识别和分析
- 错误传播路径追踪
- 问题时间线构建

**工具集**：
- `query_sls_logs`: SLS 日志查询
- `search_error_logs`: 错误日志搜索
- `search_oom_logs`: OOM 日志搜索

**分析方法**：
- 关键词组合查询
- 时间范围精确控制
- 日志级别过滤
- 上下文关联分析

### 📊 Monitoring Agent（监控分析专家）

**专业领域**：监控数据分析和性能优化

**核心能力**：
- 监控指标趋势分析
- 异常模式识别
- 性能瓶颈定位
- 容量规划建议

**工具集**：
- `query_arms_metrics`: ARMS 指标查询
- `get_cpu_usage`: CPU 使用分析
- `get_memory_usage`: 内存使用分析

**分析维度**：
- 资源使用趋势
- 性能指标变化
- 告警频率统计
- 容量预测

## 🔄 Handoff 机制

### 交接工具类型

1. **简单交接** (`transfer_to_*`)
   - 直接转交控制权
   - 保持完整的消息历史
   - 适用于简单的任务转交

2. **任务分配** (`assign_task_to_*`)
   - 包含详细的任务描述
   - 提供具体的执行要求
   - 适用于复杂任务的精确分配

### 交接流程

```python
# 1. Supervisor 分析问题
user_input = "Pod 内存不足，请分析"

# 2. 选择合适的智能体
target_agent = "oom_diagnostic_agent"

# 3. 构建任务描述
task_description = """
分析 Pod 内存不足问题：
- 检查 Pod 当前状态
- 查询内存使用历史
- 搜索 OOM 相关日志
- 提供优化建议
"""

# 4. 执行交接
assign_task_to_oom_diagnostic_agent(task_description)
```

## 💬 多轮对话支持

### 状态管理

```python
class SREMultiAgentState(MessagesState):
    last_active_agent: str = "sre_supervisor"
    current_task: str = ""
    task_status: str = "pending"
```

### 对话流程

1. **用户输入** → SRE Supervisor
2. **任务分析** → 选择专门智能体
3. **任务执行** → 专门智能体处理
4. **结果返回** → 用户
5. **后续问题** → 继续与活跃智能体对话

### 中断和恢复

- 支持用户中断当前任务
- 保持对话上下文
- 可以基于历史继续对话

## 🛠️ 实现细节

### 智能体创建

使用 `create_react_agent` 创建专门智能体：

```python
agent = create_react_agent(
    model=llm_provider,
    tools=domain_tools + handoff_tools,
    prompt=specialized_prompt,
    name=agent_name
)
```

### 图构建

使用 `StateGraph` 构建多智能体系统：

```python
builder = StateGraph(SREMultiAgentState)
builder.add_node("sre_supervisor", call_sre_supervisor)
builder.add_node("oom_diagnostic_agent", call_oom_agent)
# ... 添加其他智能体
builder.add_node("human", human_node)
```

### 检查点支持

使用 `InMemorySaver` 支持多轮对话：

```python
checkpointer = InMemorySaver()
graph = builder.compile(checkpointer=checkpointer)
```

## 📊 性能和可扩展性

### 性能优化

1. **工具缓存**：避免重复的工具发现
2. **连接复用**：复用 HTTP 连接
3. **并行处理**：支持异步操作

### 可扩展性

1. **新增智能体**：
   - 创建专门的智能体类
   - 定义专业工具集
   - 添加到系统图中

2. **新增工具**：
   - 实现工具函数
   - 添加到相应的工具集
   - 更新智能体配置

3. **新增协作模式**：
   - 定义新的 handoff 工具
   - 实现协作逻辑
   - 更新 Supervisor 决策

## 🔍 调试和监控

### 日志记录

- 智能体切换日志
- 工具调用日志
- 错误和异常日志
- 性能指标日志

### 状态追踪

- 当前活跃智能体
- 任务执行状态
- 消息历史记录
- 错误恢复过程

### 性能监控

- 响应时间统计
- 智能体使用频率
- 工具调用成功率
- 用户满意度指标

## 🚀 最佳实践

### 智能体设计

1. **单一职责**：每个智能体专注于特定领域
2. **清晰接口**：明确的输入输出定义
3. **错误处理**：优雅的错误处理和恢复
4. **文档完善**：详细的功能说明和使用示例

### 协作设计

1. **明确边界**：清晰的智能体职责边界
2. **有效沟通**：标准化的交接协议
3. **状态同步**：保持一致的状态信息
4. **冲突解决**：处理智能体间的冲突

### 用户体验

1. **透明化**：让用户了解当前处理状态
2. **可控性**：用户可以中断和引导流程
3. **一致性**：保持一致的交互体验
4. **反馈及时**：及时的进度和结果反馈
