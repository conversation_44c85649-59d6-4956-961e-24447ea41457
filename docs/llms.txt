
# Guides

- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/index/): This page provides an overview of the LangGraph project, including its logo and essential scripts for functionality within MkDocs. It also includes a reference to the README.md file for detailed information about the project. The content is designed to be user-friendly and visually appealing.
- [LangGraph Quickstart Guide](https://langchain-ai.github.io/langgraph/agents/agents/): This quickstart guide provides step-by-step instructions for setting up and using LangGraph's prebuilt components to create agentic systems. It covers prerequisites, installation, agent creation, configuration of language models, and advanced features like memory and structured output. Ideal for developers looking to leverage LangGraph for building intelligent agents.
- [Getting Started with LangGraph: Building AI Agents](https://langchain-ai.github.io/langgraph/concepts/why-langgraph/): This page provides an overview of LangGraph, a platform designed for developers to create adaptable AI agents. It highlights key features such as reliability, extensibility, and streaming support, and offers a series of tutorials to help users build a support chatbot with various capabilities. By following the tutorials, developers will learn to implement essential functionalities like conversation state management and human-in-the-loop controls.
- [Building a Basic Chatbot with LangGraph](https://langchain-ai.github.io/langgraph/tutorials/get-started/1-build-basic-chatbot/): This tutorial guides you through the process of creating a basic chatbot using LangGraph. It covers prerequisites, installation of necessary packages, and step-by-step instructions to set up a state machine for the chatbot. By the end of the tutorial, you will have a functional chatbot that can engage in simple conversations.
- [Integrating Web Search Tools into Your Chatbot](https://langchain-ai.github.io/langgraph/tutorials/get-started/2-add-tools/): This tutorial guides you through the process of enhancing your chatbot's capabilities by integrating a web search tool, specifically the Tavily Search Engine. It covers prerequisites, installation, configuration, and the implementation of the search tool within a LangGraph-based chatbot. By the end, you'll have a functional chatbot that can retrieve real-time information to answer user queries beyond its training data.
- [Implementing Memory in Chatbots with LangGraph](https://langchain-ai.github.io/langgraph/tutorials/get-started/3-add-memory/): This page provides a comprehensive guide on how to add memory functionality to chatbots using LangGraph's persistent checkpointing feature. It details the steps to create a `MemorySaver` checkpointer, compile the graph, and interact with the chatbot to maintain context across multiple interactions. Additionally, it explains how to inspect the state of the chatbot and highlights the advantages of checkpointing over simple memory solutions.
- [Implementing Human-in-the-Loop Controls in LangGraph](https://langchain-ai.github.io/langgraph/tutorials/get-started/4-human-in-the-loop/): This page provides a comprehensive guide on adding human-in-the-loop controls to LangGraph workflows, enabling agents to pause execution for human input. It details the use of the `interrupt` function to facilitate user feedback and outlines the steps to integrate a `human_assistance` tool into a chatbot. Additionally, the tutorial covers graph compilation, visualization, and resuming execution with human input.
- [Customizing State in LangGraph for Enhanced Chatbot Functionality](https://langchain-ai.github.io/langgraph/tutorials/get-started/5-customize-state/): This tutorial guides you through the process of adding custom fields to the state in LangGraph, enabling complex behaviors in your chatbot without relying solely on message lists. You will learn how to implement human-in-the-loop controls to verify information before it is stored in the state. By the end of this tutorial, you will have a deeper understanding of state management and how to enhance your chatbot's capabilities.
- [Implementing Time Travel in LangGraph Chatbots](https://langchain-ai.github.io/langgraph/tutorials/get-started/6-time-travel/): This page provides a comprehensive guide on utilizing the time travel functionality in LangGraph to enhance chatbot interactions. It covers how to rewind, add steps, and replay the state history of a chatbot, allowing users to explore different outcomes and fix mistakes. Additionally, it includes code snippets and practical examples to help developers implement these features effectively.
- [LangGraph Deployment Options](https://langchain-ai.github.io/langgraph/tutorials/deployment/): This page outlines the various options available for deploying LangGraph applications, including local testing and different cloud-based solutions. It details free deployment methods such as Local, as well as production options like Cloud SaaS and self-hosted solutions. Each deployment method is linked to further documentation for in-depth guidance.
- [Agent Development with LangGraph](https://langchain-ai.github.io/langgraph/agents/overview/): This page provides an overview of agent development using LangGraph, highlighting its prebuilt components and capabilities for building agent-based applications. It explains the structure of an agent, key features such as memory integration and human-in-the-loop control, and outlines the package ecosystem available for developers. With LangGraph, users can focus on application logic while leveraging robust infrastructure for state management and feedback.
- [Guide to Running Agents in LangGraph](https://langchain-ai.github.io/langgraph/agents/run_agents/): This page provides a comprehensive overview of how to execute agents in LangGraph, detailing both synchronous and asynchronous methods. It covers input and output formats, streaming capabilities, and how to manage execution limits to prevent infinite loops. Additionally, it includes code examples and links to further resources for deeper understanding.
- [Streaming Data in LangGraph](https://langchain-ai.github.io/langgraph/agents/streaming/): This page provides an overview of streaming data types in LangGraph, including agent progress, LLM tokens, and custom updates. It includes code examples for both synchronous and asynchronous streaming methods. Additionally, it covers how to stream multiple modes and disable streaming when necessary.
- [Configuring Chat Models for Agents](https://langchain-ai.github.io/langgraph/agents/models/): This page provides detailed instructions on how to configure various chat models for use with agents in LangChain. It covers model initialization, tool calling support, and how to specify models from different providers such as OpenAI, Anthropic, Azure, Google Gemini, and AWS Bedrock. Additionally, it includes information on disabling streaming, adding model fallbacks, and links to further resources.
- [Using Tools in LangChain](https://langchain-ai.github.io/langgraph/agents/tools/): This page provides an overview of how to define, customize, and manage tools within the LangChain framework. It covers creating simple tools, handling tool errors, and utilizing prebuilt integrations for enhanced functionality. Additionally, it discusses advanced features such as memory management and controlling tool behavior during agent execution.
- [Integrating MCP with LangGraph Agents](https://langchain-ai.github.io/langgraph/agents/mcp/): This page provides a comprehensive guide on how to integrate the Model Context Protocol (MCP) with LangGraph agents using the `langchain-mcp-adapters` library. It includes installation instructions, example code for using MCP tools, and guidance on creating custom MCP servers. Additional resources for further reading on MCP are also provided.
- [Understanding Context in LangGraph Agents](https://langchain-ai.github.io/langgraph/agents/context/): This page provides an overview of how to supply context to agents in LangGraph, detailing the three primary types: Config, State, and Long-Term Memory. It explains how to use these context types to enhance agent behavior, customize prompts, and access context in tools. Additionally, it includes code examples for implementing context in various scenarios.
- [Understanding Memory in LangGraph for Conversational Agents](https://langchain-ai.github.io/langgraph/agents/memory/): This documentation page provides an overview of the two types of memory supported by LangGraph: short-term and long-term memory. It explains how to implement these memory types in conversational agents, including code examples and best practices for managing message history. Additionally, it covers the use of persistent storage and tools for enhancing memory functionality.
- [Implementing Human-in-the-Loop in LangGraph](https://langchain-ai.github.io/langgraph/agents/human-in-the-loop/): This documentation page provides a comprehensive guide on how to implement Human-in-the-Loop (HIL) features in LangGraph, allowing for human review and approval of tool calls in agents. It covers the use of the `interrupt()` function to pause execution for human input, along with practical examples and code snippets. Additionally, it explains how to create a wrapper to add HIL capabilities to any tool seamlessly.
- [Building Multi-Agent Systems](https://langchain-ai.github.io/langgraph/agents/multi-agent/): This page provides an overview of multi-agent systems, detailing how to create and manage them using supervisor and swarm architectures. It includes practical examples of implementing a flight and hotel booking assistant using the LangGraph libraries. Additionally, the page explains the concept of handoffs between agents, allowing for seamless communication and task delegation.
- [Evaluating Agent Performance with LangSmith](https://langchain-ai.github.io/langgraph/agents/evals/): This page provides a comprehensive guide on how to evaluate the performance of agents using the LangSmith evaluations framework. It includes instructions on defining evaluator functions, utilizing prebuilt evaluators from the AgentEvals package, and running evaluations with specific datasets. Additionally, it covers different evaluation techniques, including trajectory matching and using LLMs as judges.
- [Deploying Your LangGraph Agent](https://langchain-ai.github.io/langgraph/agents/deployment/): This page provides a comprehensive guide on how to deploy a LangGraph agent, including setting up a LangGraph app for both local development and production. It covers essential features, installation steps, and configuration requirements, along with instructions for launching the local server and utilizing the LangGraph Studio Web UI for debugging. Additionally, it offers links to further resources for deployment options.
- [Agent Chat UI Documentation](https://langchain-ai.github.io/langgraph/agents/ui/): This page provides comprehensive guidance on using the Agent Chat UI for interacting with LangGraph agents. It covers setup instructions, features like human-in-the-loop workflows, and the integration of generative UI components. Users can find links to relevant resources and tips for customizing their chat experience.
- [Overview of Agent Architectures in LLM Applications](https://langchain-ai.github.io/langgraph/concepts/agentic_concepts/): This page provides a comprehensive overview of various agent architectures used in large language model (LLM) applications, highlighting their control flows and functionalities. It discusses key concepts such as routers, tool-calling agents, memory management, and planning, along with customization options for specific tasks. Additionally, it covers advanced features like human-in-the-loop, parallelization, subgraphs, and reflection mechanisms to enhance agent performance.
- [Understanding Workflows and Agents in LangGraph](https://langchain-ai.github.io/langgraph/tutorials/workflows/): This documentation page provides an in-depth overview of workflows and agents within LangGraph, highlighting their differences and use cases. It covers various patterns for building agentic systems, including setup instructions, building blocks, and advanced concepts like prompt chaining, parallelization, and routing. Additionally, it offers practical examples and code snippets to help users implement these workflows effectively.
- [Understanding LangGraph: Core Concepts and Components](https://langchain-ai.github.io/langgraph/concepts/low_level/): This documentation page provides an in-depth overview of the core concepts of LangGraph, focusing on how agent workflows are modeled as graphs. It covers essential components such as States, Nodes, and Edges, and explains how they interact to create complex workflows. Additionally, it discusses graph compilation, message handling, and configuration options to enhance the functionality of your graphs.
- [LangGraph Runtime Overview](https://langchain-ai.github.io/langgraph/concepts/pregel/): This page provides a comprehensive overview of the LangGraph runtime, specifically focusing on the Pregel execution model. It details the structure and functionality of actors and channels within the Pregel framework, along with examples of how to implement applications. Additionally, it introduces high-level APIs for creating Pregel applications using StateGraph and Functional API.
- [Using the LangGraph API: A Comprehensive Guide](https://langchain-ai.github.io/langgraph/how-tos/graph-api/): This documentation provides a detailed overview of how to utilize the LangGraph Graph API, covering essential concepts such as state management, node creation, and control flow. It includes practical examples for building sequences, branches, and loops, as well as advanced features like retry policies and async execution. Additionally, the guide offers insights into visualizing graphs and integrating with external tools.
- [LangGraph Streaming System](https://langchain-ai.github.io/langgraph/concepts/streaming/): This page provides an overview of the streaming capabilities of LangGraph, enabling real-time updates for enhanced user experiences. It details the types of data that can be streamed, including workflow progress, LLM tokens, and custom updates. Additionally, it outlines various functionalities and modes available for streaming within the LangGraph framework.
- [Streaming Outputs in LangGraph](https://langchain-ai.github.io/langgraph/how-tos/streaming/): This documentation page provides an overview of how to utilize the streaming capabilities of LangGraph, including synchronous and asynchronous streaming methods. It covers various stream modes, such as updates, values, and custom data, along with examples of how to implement them in your graphs. Additionally, it discusses the integration of Large Language Models (LLMs) and how to handle streaming outputs effectively.
- [LangGraph Persistence and Checkpointing](https://langchain-ai.github.io/langgraph/concepts/persistence/): This page provides an in-depth overview of the persistence layer in LangGraph, focusing on the use of checkpointers to save graph states at each super-step. It covers key concepts such as threads, checkpoints, state retrieval, and memory management, along with practical examples and code snippets. Additionally, it discusses advanced features like time travel, fault tolerance, and the integration of memory stores for cross-thread information retention.
- [Understanding Durable Execution in LangGraph](https://langchain-ai.github.io/langgraph/concepts/durable_execution/): This page provides an overview of durable execution, a technique that allows workflows to save their progress and resume from key points. It details the requirements for implementing durable execution in LangGraph, including the use of persistence and tasks to ensure deterministic and consistent replay. Additionally, it covers how to handle pausing, resuming, and recovering workflows effectively.
- [Implementing Memory in LangGraph for AI Applications](https://langchain-ai.github.io/langgraph/how-tos/persistence/): This documentation page provides a comprehensive guide on adding persistence to AI applications using LangGraph. It covers both short-term and long-term memory implementations, including code examples for managing conversation context and user-specific data. Additionally, it discusses the use of various storage backends and semantic search capabilities for enhanced memory management.
- [Understanding Memory in AI Agents](https://langchain-ai.github.io/langgraph/concepts/memory/): This documentation page provides an in-depth overview of memory types in AI agents, focusing on short-term and long-term memory. It explains how these memory types can be implemented and managed within applications using LangGraph, including techniques for handling conversation history and storing memories. Additionally, it discusses the importance of memory in enhancing user interactions and the various strategies for writing and updating memories.
- [Memory Management in LangGraph for AI Applications](https://langchain-ai.github.io/langgraph/how-tos/memory/): This page provides an overview of memory management in LangGraph, focusing on short-term and long-term memory functionalities essential for conversational agents. It includes detailed instructions on how to implement memory strategies such as trimming, summarizing, and deleting messages to optimize conversation tracking without exceeding context limits. Code examples are provided to illustrate the implementation of these memory management techniques.
- [Human-in-the-Loop Workflows in LangGraph](https://langchain-ai.github.io/langgraph/concepts/human_in_the_loop/): This page provides an overview of the human-in-the-loop (HIL) capabilities within LangGraph, highlighting how human intervention can enhance automated processes. It details key features such as persistent execution state and flexible integration points, along with typical use cases for validating outputs and providing context. Additionally, it outlines the implementation of HIL through specific functions and primitives.
- [Implementing Human-in-the-Loop Workflows with Interrupts](https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/add-human-in-the-loop/): This documentation page provides a comprehensive guide on using the `interrupt` function in LangGraph to facilitate human-in-the-loop workflows. It covers the implementation details, design patterns, and best practices for pausing graph execution to gather human input, as well as how to resume execution with that input. Additionally, it highlights common pitfalls and offers extended examples to illustrate various use cases.
- [Understanding Breakpoints in LangGraph](https://langchain-ai.github.io/langgraph/concepts/breakpoints/): This page provides an overview of breakpoints in LangGraph, which allow users to pause graph execution at specific points for inspection. It explains how breakpoints utilize the persistence layer to save the graph state and how execution can be resumed after inspection. An illustrative example is included to demonstrate the concept visually.
- [Using Breakpoints in Graph Execution](https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/breakpoints/): This page provides a comprehensive guide on how to implement breakpoints in graph execution for debugging purposes. It covers the requirements for setting breakpoints, the difference between static and dynamic breakpoints, and includes code examples for both compile-time and run-time configurations. Additionally, it explains how to manage breakpoints in subgraphs.
- [Time Travel Functionality in LangGraph](https://langchain-ai.github.io/langgraph/concepts/time-travel/): This page explains the time travel feature in LangGraph, which allows users to analyze and debug decision-making processes in non-deterministic systems. It outlines how to understand reasoning, debug mistakes, and explore alternative solutions by resuming execution from prior checkpoints. The functionality enables users to create new forks in the execution history for deeper insights.
- [Using Time-Travel in LangGraph](https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/time-travel/): This page provides a comprehensive guide on how to implement time-travel functionality in LangGraph. It outlines the steps to run a graph, identify checkpoints, modify graph states, and resume execution from specific checkpoints. Additionally, an example workflow is included to illustrate the process of generating and modifying jokes using LangGraph.
- [Integrating Tools with AI Models](https://langchain-ai.github.io/langgraph/concepts/tools/): This page provides an overview of how AI models can interact with external systems using tool calling. It explains the concept of tools, their integration with chat models, and how to create or use prebuilt tools for various applications. Additionally, it highlights the importance of relevance in tool invocation and offers links to further resources and guides.
- [Using Tools in LangChain](https://langchain-ai.github.io/langgraph/how-tos/tool-calling/): This documentation page provides a comprehensive guide on how to create and utilize tools within the LangChain framework. It covers defining simple and customized tools, managing tool arguments, accessing configuration and state, and integrating tools with chat models and agents. Additionally, it discusses error handling and strategies for managing a large number of tools.
- [Understanding Subgraphs in LangGraph](https://langchain-ai.github.io/langgraph/concepts/subgraphs/): This page provides an overview of subgraphs in LangGraph, explaining their role as encapsulated nodes within larger graphs. It discusses the benefits of using subgraphs, such as facilitating multi-agent systems and enabling independent team work. Additionally, it outlines the communication methods between parent graphs and subgraphs, detailing scenarios involving shared and different state schemas.
- [Using Subgraphs in LangGraph](https://langchain-ai.github.io/langgraph/how-tos/subgraph/): This guide provides an overview of how to effectively use subgraphs within LangGraph, including communication methods between parent graphs and subgraphs. It covers shared and different state schemas, setup instructions, and examples for implementing subgraphs in multi-agent systems. Additionally, it discusses persistence, state management, and streaming outputs from subgraphs.
- [Understanding Multi-Agent Systems](https://langchain-ai.github.io/langgraph/concepts/multi_agent/): This page provides an in-depth overview of multi-agent systems, focusing on the architecture and benefits of using multiple independent agents to manage complex applications. It discusses various multi-agent architectures, including network, supervisor, and hierarchical models, as well as communication strategies and state management techniques for effective agent interaction.
- [Building Multi-Agent Systems with LangGraph](https://langchain-ai.github.io/langgraph/how-tos/multi_agent/): This guide provides an overview of how to build multi-agent systems using LangGraph, focusing on the implementation of handoffs for agent communication. It covers the creation of independent agents, the use of handoffs to transfer control and data between agents, and examples of prebuilt multi-agent architectures. Additionally, it includes code snippets and best practices for managing agent interactions and state.
- [Understanding the Functional API in LangGraph](https://langchain-ai.github.io/langgraph/concepts/functional_api/): This documentation page provides an overview of the Functional API in LangGraph, detailing its key features such as persistence, memory, and human-in-the-loop capabilities. It explains how to define workflows using the `@entrypoint` and `@task` decorators, along with examples and best practices for implementing workflows with state management and streaming. Additionally, it compares the Functional API with the Graph API, highlighting their differences and use cases.
- [Functional API Documentation](https://langchain-ai.github.io/langgraph/how-tos/use-functional-api/): This page provides comprehensive guidance on using the Functional API, including creating workflows, handling parallel execution, and integrating with other APIs. It covers various features such as retry policies, caching, and human-in-the-loop workflows, along with practical examples. Additionally, it discusses memory management strategies for both short-term and long-term use cases.
- [Overview of LangGraph Platform](https://langchain-ai.github.io/langgraph/concepts/langgraph_platform/): The LangGraph Platform is designed for developing, deploying, and managing long-running agent workflows with ease. This page outlines the platform's features, including streaming support, background runs, and memory management, which enhance the performance and reliability of agent applications. Additionally, it provides links to resources for getting started and deploying agents effectively.
- [LangGraph Platform Quickstart Guide](https://langchain-ai.github.io/langgraph/tutorials/langgraph-platform/local-server/): This quickstart guide provides step-by-step instructions for running a LangGraph application locally. It covers prerequisites, installation of the LangGraph CLI, app creation, dependency installation, and launching the server. Additionally, it includes testing your application using the LangGraph Studio and API.
- [LangGraph Platform Deployment Quickstart](https://langchain-ai.github.io/langgraph/cloud/quick_start/): This quickstart guide provides step-by-step instructions for deploying an application on the LangGraph Platform using GitHub. It covers prerequisites, repository creation, deployment procedures, and testing your application and API. Follow these steps to successfully set up and run your application in the LangGraph environment.
- [Overview of LangGraph Platform Components](https://langchain-ai.github.io/langgraph/concepts/langgraph_components/): This page provides a comprehensive overview of the various components that make up the LangGraph Platform. It details the functionalities of each component, including the LangGraph Server, CLI, Studio, SDKs, and the control and data planes. Users can learn how these components work together to facilitate the development, deployment, and management of LangGraph applications.
- [LangGraph Server Documentation](https://langchain-ai.github.io/langgraph/concepts/langgraph_server/): This page provides an overview of the LangGraph Server, an API designed for creating and managing agent-based applications. It details the server versions, application structure, deployment components, and the use of assistants, persistence, and task queues. Additionally, it includes links to further resources and guides for effective deployment and usage.
- [LangGraph Application Structure Guide](https://langchain-ai.github.io/langgraph/concepts/application_structure/): This page provides an overview of the structure of a LangGraph application, detailing the essential components such as the configuration file, dependencies, graphs, and environment variables. It includes examples of directory structures for both Python and JavaScript applications, as well as guidance on how to specify the necessary information for deployment. Additionally, it covers key concepts related to the configuration file and the role of dependencies and environment variables in the application.
- [Setting Up a LangGraph Application with requirements.txt](https://langchain-ai.github.io/langgraph/cloud/deployment/setup/): This guide provides step-by-step instructions for configuring a LangGraph application for deployment using a requirements.txt file to manage dependencies. It covers essential topics such as specifying dependencies, defining environment variables, and creating the LangGraph configuration file. Additionally, it includes examples and tips for alternative setup methods.
- [Setting Up a LangGraph Application with pyproject.toml](https://langchain-ai.github.io/langgraph/cloud/deployment/setup_pyproject/): This guide provides step-by-step instructions for configuring a LangGraph application using the `pyproject.toml` file for dependency management. It covers the necessary components, including specifying dependencies, environment variables, and defining graphs, along with examples and best practices. Additionally, it offers tips for alternative setups and links to further resources for deployment.
- [Setting Up a LangGraph.js Application](https://langchain-ai.github.io/langgraph/cloud/deployment/setup_javascript/): This guide provides step-by-step instructions for configuring a LangGraph.js application for deployment on the LangGraph Platform or for self-hosting. It covers essential topics such as specifying dependencies, environment variables, defining graphs, and creating the necessary configuration file. By following this walkthrough, users will learn how to structure their application and prepare it for deployment.
- [Customizing Your Dockerfile in LangGraph](https://langchain-ai.github.io/langgraph/cloud/deployment/custom_docker/): This page provides a guide on how to customize your Dockerfile by adding additional commands through the `langgraph.json` configuration file. It explains how to specify the `dockerfile_lines` key to include necessary dependencies, such as installing system packages and Python libraries. An example is provided to illustrate the process of integrating the Pillow library for image processing.
- [LangGraph CLI Documentation](https://langchain-ai.github.io/langgraph/concepts/langgraph_cli/): This page provides an overview of the LangGraph CLI, a command-line tool for building and running the LangGraph API server locally. It includes installation instructions, a list of core commands, and their descriptions to help users effectively utilize the CLI for development and deployment. For further details, users can refer to the LangGraph CLI Reference.
- [LangGraph Studio Documentation](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/): This page provides an overview of LangGraph Studio, an IDE for visualizing, interacting with, and debugging agentic systems that utilize the LangGraph Server API. It outlines the prerequisites for using the studio, key features, and the two operational modes: Graph mode and Chat mode. Additionally, it includes links to further resources for getting started with LangGraph Studio.
- [Getting Started with LangGraph Studio](https://langchain-ai.github.io/langgraph/cloud/how-tos/studio/quick_start/): This page provides a comprehensive guide on how to connect and use LangGraph Studio with both deployed applications on the LangGraph Platform and local development servers. It includes instructions for installation, running the server, accessing the Studio UI, and debugging options. Additionally, troubleshooting tips and next steps for further exploration of LangGraph Studio features are also provided.
- [Running Applications: A Comprehensive Guide](https://langchain-ai.github.io/langgraph/cloud/how-tos/invoke_studio/): This page provides a detailed guide on how to submit a run to your application, covering both Graph and Chat modes. It includes instructions on specifying input, managing assistants, enabling streaming, and using breakpoints. Additionally, it offers tips for running applications from specific checkpoints in existing threads.
- [Managing Assistants in LangGraph Studio](https://langchain-ai.github.io/langgraph/cloud/how-tos/studio/manage_assistants/): This page provides guidance on how to manage assistants within LangGraph Studio, including viewing, editing, and updating assistant configurations. It covers both Graph mode and Chat mode, detailing how to activate assistants and make changes to their settings. Users will learn how to navigate the interface to effectively manage their assistant configurations for graph runs.
- [Managing Threads in Studio](https://langchain-ai.github.io/langgraph/cloud/how-tos/threads_studio/): This page provides a comprehensive guide on how to view and edit threads within the Studio application. It covers both Graph and Chat modes, detailing the steps to create new threads, view thread history, and edit thread states. Additionally, it includes links to related concepts for further learning.
- [Modifying Prompts in LangGraph Studio](https://langchain-ai.github.io/langgraph/cloud/how-tos/iterate_graph_studio/): This page provides guidance on how to modify prompts within LangGraph Studio using two methods: direct node editing and the LangSmith Playground interface. It details the configuration options available for nodes, including `langgraph_nodes` and `langgraph_type`, along with examples for both Pydantic models and dataclasses. Additionally, it outlines the steps for editing prompts in the UI and utilizing the LangSmith Playground for testing LLM calls.
- [Debugging LangSmith Traces in LangGraph Studio](https://langchain-ai.github.io/langgraph/cloud/how-tos/clone_traces_studio/): This guide provides step-by-step instructions for opening and debugging LangSmith traces in LangGraph Studio. It covers how to deploy threads and test local agents with remote traces, ensuring a seamless debugging experience. Additionally, it outlines the requirements for local agents and the process for cloning threads for local testing.
- [How to Add Nodes to LangSmith Datasets](https://langchain-ai.github.io/langgraph/cloud/how-tos/datasets_studio/): This guide provides step-by-step instructions on how to add examples from nodes in the thread log to LangSmith datasets. It covers selecting threads, choosing nodes, and editing inputs/outputs before adding them to the dataset. Additionally, it includes links to further resources on evaluating intermediate steps.
- [LangGraph SDK Documentation](https://langchain-ai.github.io/langgraph/concepts/sdk/): This page provides an overview of the LangGraph SDK, including installation instructions for both Python and JavaScript. It details the synchronous and asynchronous client options available for interacting with the LangGraph Server. Additionally, it offers links to further resources and references for the SDK.
- [Integrating Semantic Search in LangGraph](https://langchain-ai.github.io/langgraph/cloud/deployment/semantic_search/): This guide provides step-by-step instructions on how to implement semantic search in your LangGraph deployment. It covers prerequisites, configuration of the store, and usage examples for searching memories and documents by semantic similarity. Additionally, it includes information on using custom embeddings and querying via the LangGraph SDK.
- [Configuring Time-to-Live (TTL) in LangGraph Applications](https://langchain-ai.github.io/langgraph/how-tos/ttl/configure_ttl/): This guide provides detailed instructions on how to configure Time-to-Live (TTL) settings for checkpoints and store items in LangGraph applications. It covers the necessary configurations in the `langgraph.json` file, including strategies for managing data lifecycle and memory. Additionally, it explains how to combine TTL configurations and override them at runtime.
- [LangGraph Authentication & Access Control Overview](https://langchain-ai.github.io/langgraph/concepts/auth/): This page provides a comprehensive guide to the authentication and authorization mechanisms within the LangGraph Platform. It explains the core concepts of authentication versus authorization, outlines default security models, and details the system architecture involved in user identity management. Additionally, it covers implementation examples for authentication and authorization handlers, along with common access patterns and supported resources.
- [Custom Authentication Setup for LangGraph Platform](https://langchain-ai.github.io/langgraph/how-tos/auth/custom_auth/): This guide provides step-by-step instructions on how to implement custom authentication in your LangGraph Platform application. It covers the necessary prerequisites, implementation details, configuration updates, and client connection methods. The guide is applicable to both managed and Enterprise self-hosted deployments.
- [Documenting API Authentication in OpenAPI for LangGraph](https://langchain-ai.github.io/langgraph/how-tos/auth/openapi_security/): This guide provides instructions on how to customize the security schema for your LangGraph Platform API documentation using OpenAPI. It covers default security schemes for both LangGraph Platform and self-hosted deployments, as well as how to implement custom authentication. Additionally, it includes examples for OAuth2 and API key authentication, along with testing procedures.
- [Managing Assistants in LangGraph](https://langchain-ai.github.io/langgraph/concepts/assistants/): This page provides an overview of how to create and manage assistants within the LangGraph Platform, which allows for separate configuration of agents without altering the core graph logic. It covers the prerequisites, configuration options, and versioning of assistants, highlighting their role in optimizing agent performance for different tasks. Additionally, it includes links to relevant API references and how-to guides for further assistance.
- [Managing Assistants in LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/configuration_cloud/): This documentation page provides a comprehensive guide on how to create, configure, and manage assistants using the LangGraph SDK and Platform UI. It includes code examples in Python and JavaScript, as well as instructions for creating new versions and using previous versions of assistants. Additionally, it covers the process of utilizing assistants in various environments.
- [Understanding Threads in LangGraph](https://langchain-ai.github.io/langgraph/cloud/concepts/threads/): This page provides an overview of threads in the LangGraph framework, detailing how they accumulate the state of runs and the importance of checkpoints. It explains the process of creating threads and retrieving their current and historical states. Additionally, it offers links to further resources on threads, checkpoints, and the LangGraph API for managing thread states.
- [Managing Threads in LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/use_threads/): This documentation page provides a comprehensive guide on how to create, view, and inspect threads using the LangGraph SDK. It includes detailed instructions for creating empty threads, copying existing threads, and initializing threads with prepopulated states. Additionally, it covers how to list and inspect threads, including filtering and sorting options.
- [Understanding Runs in LangGraph Platform](https://langchain-ai.github.io/langgraph/cloud/concepts/runs/): This page provides an overview of what constitutes a run in the LangGraph Platform, including its input, configuration, and metadata. It also highlights the ability to execute runs on threads and offers links to the API reference for managing runs.
- [Starting Background Runs for Your Agent](https://langchain-ai.github.io/langgraph/cloud/how-tos/background_run/): This guide provides step-by-step instructions on how to initiate background runs for your agent using Python, JavaScript, and CURL. It covers the setup process, checking current runs, starting new runs, and retrieving the final results. By following this documentation, users can efficiently manage long-running jobs within their applications.
- [Running Multiple Agents on the Same Thread in LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/same-thread/): This documentation page explains how to run multiple agents on the same thread using the LangGraph Platform. It provides step-by-step examples in Python, JavaScript, and CURL to create agents, run them on a thread, and demonstrate how the second agent can utilize the context from the first agent's responses. By following the examples, users can learn to effectively manage multiple agents and their interactions.
- [Scheduling Cron Jobs with LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/cron_jobs/): This page provides a comprehensive guide on how to schedule cron jobs using the LangGraph Platform. It includes setup instructions for different programming languages, examples of creating and deleting cron jobs, and details on managing stateless cron jobs. Users will learn how to automate tasks such as sending weekly emails without writing custom scripts.
- [Guide to Stateless Runs in LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/stateless_runs/): This page provides a comprehensive guide on how to implement stateless runs using the LangGraph Platform. It includes setup instructions for various programming languages, examples of streaming results, and methods for waiting for stateless results. Users will learn how to execute runs without maintaining persistent state, making their applications more efficient.
- [Configurable Headers in LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/configurable_headers/): This page provides guidance on how to configure headers dynamically in the LangGraph platform to modify agent behavior and permissions. It details how to include or exclude specific headers in the runtime configuration using the `langgraph.json` file. Additionally, it explains how to access these headers within your graph and offers an option to opt-out of configurable headers.
- [Streaming in LangGraph Platform](https://langchain-ai.github.io/langgraph/cloud/concepts/streaming/): This page provides an overview of streaming capabilities within the LangGraph Platform, detailing the various streaming modes available for LLM applications. It includes instructions for creating streaming runs, handling stateless runs, and joining active background runs. Additionally, code examples in Python, JavaScript, and cURL are provided to illustrate the implementation of these features.
- [Streaming Outputs with LangGraph SDK](https://langchain-ai.github.io/langgraph/cloud/how-tos/streaming/): This documentation page provides detailed instructions on how to stream outputs from the LangGraph API server using the LangGraph SDK in Python, JavaScript, and cURL. It covers various streaming modes, including updates, values, and custom data, along with examples for each mode. Additionally, it explains how to handle subgraphs, debug information, and LLM tokens during streaming.
- [Human-in-the-Loop Workflows in LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/add-human-in-the-loop/): This page provides an overview of the human-in-the-loop (HIL) capabilities in LangGraph, allowing for human intervention in automated processes. It details the `interrupt` function, which pauses execution for human input, and includes examples in Python, JavaScript, and cURL for implementing HIL workflows. Additionally, it links to further resources for understanding and utilizing HIL features effectively.
- [Using Breakpoints in LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/human_in_the_loop_breakpoint/): This page provides an overview of how to set and use breakpoints in LangGraph to pause graph execution for inspection. It includes examples for setting breakpoints at compile time and run time in Python, JavaScript, and cURL. Additionally, it offers guidance on resuming execution after hitting a breakpoint.
- [Using Time Travel in LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/human_in_the_loop_time_travel/): This page provides a comprehensive guide on how to utilize the time travel functionality in LangGraph, allowing users to resume execution from previous checkpoints. It outlines the steps to run a graph, identify checkpoints, modify graph states, and resume execution. Additionally, the page includes code examples in Python, JavaScript, and cURL for practical implementation.
- [Model Context Protocol (MCP) Endpoint Documentation](https://langchain-ai.github.io/langgraph/concepts/server-mcp/): This page provides comprehensive documentation on the Model Context Protocol (MCP) endpoint available in LangGraph Server. It covers the requirements for using MCP, how to expose agents as MCP tools, and includes examples for connecting with MCP-compliant clients in various programming languages. Additionally, it outlines session behavior, authentication, and instructions for disabling the MCP endpoint.
- [Managing Double Texting in LangGraph](https://langchain-ai.github.io/langgraph/concepts/double_texting/): This page provides an overview of how to handle double texting scenarios in LangGraph, where users may send multiple messages before the first has completed. It outlines four strategies: Reject, Enqueue, Interrupt, and Rollback, each with links to detailed configuration guides. Prerequisites for implementing these strategies include having the LangGraph Server set up.
- [Using the Interrupt Option in Double Texting](https://langchain-ai.github.io/langgraph/cloud/how-tos/interrupt_concurrent/): This guide provides detailed instructions on how to utilize the `interrupt` option for double texting, allowing users to interrupt a prior run of a graph and start a new one. It includes setup instructions, code examples in Python, JavaScript, and CURL, as well as guidance on viewing run results and verifying the status of interrupted runs. Familiarity with double texting is assumed, and a link to a conceptual guide is provided for further understanding.
- [Using the Rollback Option in Double Texting](https://langchain-ai.github.io/langgraph/cloud/how-tos/rollback_concurrent/): This guide provides detailed instructions on how to utilize the `rollback` option in double texting, which allows users to interrupt a previous run and start a new one while permanently deleting the prior run from the database. It includes setup instructions, code examples in Python, JavaScript, and CURL, and demonstrates how to view run results and verify the deletion of the original run. Familiarity with double texting is assumed, and a link to a conceptual guide is provided for further reading.
- [Using the Reject Option in Double Texting](https://langchain-ai.github.io/langgraph/cloud/how-tos/reject_concurrent/): This guide provides an overview of the `reject` option in double texting, which prevents new runs of a graph from starting while an original run is still in progress. It includes setup instructions, code examples in Python, JavaScript, and CURL, and demonstrates how to handle errors when attempting to create concurrent runs. Additionally, it shows how to view the results of the original run after the rejection.
- [Using the Enqueue Option for Double Texting](https://langchain-ai.github.io/langgraph/cloud/how-tos/enqueue_concurrent/): This guide provides an overview of the `enqueue` option for double texting, which allows interruptions to be queued and executed in the order they are received. It includes setup instructions, code examples in Python, JavaScript, and CURL for creating runs, and methods for viewing run results. Familiarity with double texting concepts is assumed, and a helper function for output formatting is also provided.
- [Understanding Webhooks in LangGraph Platform](https://langchain-ai.github.io/langgraph/cloud/concepts/webhooks/): This page provides an overview of webhooks and their role in enabling event-driven communication between LangGraph Platform applications and external services. It explains how to use the `webhook` parameter in various endpoints to trigger requests upon the completion of API calls. For further details, a link to a comprehensive how-to guide is also included.
- [Using Webhooks with LangGraph Platform](https://langchain-ai.github.io/langgraph/cloud/how-tos/webhooks/): This documentation page provides a comprehensive guide on how to implement webhooks in the LangGraph Platform to receive updates after API calls. It includes details on supported endpoints, setup instructions for different programming languages, and examples of how to specify webhook parameters in API requests. Additionally, it covers security measures and testing tools for verifying webhook functionality.
- [Scheduling Tasks with Cron Jobs on LangGraph Platform](https://langchain-ai.github.io/langgraph/cloud/concepts/cron_jobs/): This page provides an overview of how to use cron jobs on the LangGraph Platform to run assistants on a defined schedule. It explains the process of setting up a cron job, including specifying the schedule, assistant, and input. Additionally, it includes links to a how-to guide and API reference for further details.
- [Scheduling Cron Jobs with LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/cron_jobs/): This page provides a comprehensive guide on how to use cron jobs with the LangGraph Platform to automate graph executions on a schedule. It includes setup instructions for various programming languages, examples of creating and deleting cron jobs, and tips for managing stateless cron jobs. Users will learn how to efficiently schedule tasks without manual intervention, ensuring timely execution of automated processes.
- [Adding Custom Lifespan Events in LangGraph](https://langchain-ai.github.io/langgraph/how-tos/http/custom_lifespan/): This page provides a guide on how to implement custom lifespan events in your LangGraph Platform applications, specifically for Python deployments. It covers the initialization and cleanup of resources during server startup and shutdown using FastAPI. Additionally, it includes code examples and configuration steps to help you integrate these events into your application.
- [Adding Custom Middleware to LangGraph Platform](https://langchain-ai.github.io/langgraph/how-tos/http/custom_middleware/): This page provides a step-by-step guide on how to add custom middleware to your server when deploying agents to the LangGraph Platform. It covers the necessary code implementation using FastAPI, configuration settings in `langgraph.json`, and instructions for testing and deploying your application. Additionally, it offers links to related topics such as custom routes and lifespan events for further customization.
- [Adding Custom Routes in LangGraph](https://langchain-ai.github.io/langgraph/how-tos/http/custom_routes/): This page provides a step-by-step guide on how to add custom routes to your LangGraph platform application using a Starlette or FastAPI app. It includes instructions for creating a new app, configuring the `langgraph.json` file, and testing the server locally. Additionally, it explains how custom routes can override default endpoints and offers suggestions for further customization.
- [LangGraph Deployment Options](https://langchain-ai.github.io/langgraph/concepts/deployment_options/): This page outlines the various deployment options available for the LangGraph Platform, including Cloud SaaS, Self-Hosted Data Plane, Self-Hosted Control Plane, and Standalone Container. Each option is described in detail, highlighting key features, management responsibilities, and compatibility. A comparison table is also provided to help users choose the best deployment strategy for their needs.
- [LangGraph Data Plane Overview](https://langchain-ai.github.io/langgraph/concepts/langgraph_data_plane/): This page provides a comprehensive overview of the LangGraph Data Plane, detailing its components including the server infrastructure, listener application, and data management systems like Postgres and Redis. It also covers key features such as autoscaling, static IP addresses, and custom configurations for Postgres and Redis. Additionally, the page outlines telemetry, licensing, and tracing functionalities relevant to different deployment options.
- [LangGraph Control Plane Overview](https://langchain-ai.github.io/langgraph/concepts/langgraph_control_plane/): This page provides a comprehensive overview of the LangGraph Control Plane, detailing its UI and API functionalities for managing LangGraph Servers. It covers deployment types, environment variables, database provisioning, and asynchronous deployment processes. Additionally, it highlights the integration with LangSmith for tracing projects.
- [Cloud SaaS Deployment Guide](https://langchain-ai.github.io/langgraph/concepts/langgraph_cloud/): This page provides a comprehensive guide on deploying the LangGraph Server using the Cloud SaaS model. It outlines the roles of the control plane and data plane, detailing their functionalities and management. Additionally, it includes an architectural diagram to illustrate the deployment structure.
- [Deployment Guide for LangGraph Platform](https://langchain-ai.github.io/langgraph/cloud/deployment/cloud/): This page provides a comprehensive guide on how to deploy applications to the LangGraph Platform using GitHub repositories. It covers prerequisites, steps for creating new deployments and revisions, managing deployment settings, and viewing logs. Additionally, it includes instructions for whitelisting IP addresses and modifying GitHub repository access.
- [Self-Hosted Data Plane Deployment Guide](https://langchain-ai.github.io/langgraph/concepts/langgraph_self_hosted_data_plane/): This page provides an overview of the Self-Hosted Data Plane deployment option, which allows users to manage their data plane infrastructure while offloading control plane management to LangChain. It outlines the requirements, architecture, and supported compute platforms for deployment. Additionally, it includes important information regarding the beta status of this deployment option.
- [Deploying a Self-Hosted Data Plane](https://langchain-ai.github.io/langgraph/cloud/deployment/self_hosted_data_plane/): This page provides a comprehensive guide on deploying a Self-Hosted Data Plane using Kubernetes and Amazon ECS. It outlines the prerequisites, setup steps, and configuration details necessary for a successful deployment. Additionally, it highlights the current beta status of this deployment option.
- [Self-Hosted Control Plane Deployment Guide](https://langchain-ai.github.io/langgraph/concepts/langgraph_self_hosted_control_plane/): This page provides an overview of the Self-Hosted Control Plane deployment option, currently in beta. It outlines the requirements, architecture, and compute platforms supported for deploying the control and data planes in your cloud environment. Additionally, it includes important links and resources for managing your self-hosted infrastructure.
- [Deploying a Self-Hosted Control Plane](https://langchain-ai.github.io/langgraph/cloud/deployment/self_hosted_control_plane/): This page provides a comprehensive guide on deploying a Self-Hosted Control Plane using Kubernetes. It outlines the prerequisites, setup steps, and configuration details necessary for a successful deployment. Additionally, it highlights the beta status of this deployment option and includes links to relevant resources for further assistance.
- [Deploying LangGraph Server with Standalone Container](https://langchain-ai.github.io/langgraph/concepts/langgraph_standalone_container/): This page provides a comprehensive guide on deploying a LangGraph Server using the Standalone Container option. It outlines the architecture, supported compute platforms, and Enterprise server version features. Users will find essential information on managing the data plane infrastructure without a control plane.
- [Deploying a Standalone Container with LangGraph](https://langchain-ai.github.io/langgraph/cloud/deployment/standalone_container/): This documentation provides a comprehensive guide on deploying a standalone container for the LangGraph application. It covers prerequisites, environment variable configurations, and deployment methods using Docker and Docker Compose. Additionally, it includes instructions for deploying on Kubernetes using Helm.
- [Scalability and Resilience of LangGraph Platform](https://langchain-ai.github.io/langgraph/concepts/scalability_and_resilience/): This page provides an overview of the scalability and resilience features of the LangGraph Platform. It details how the platform handles server and queue scalability, as well as the mechanisms in place for ensuring resilience during both graceful and hard shutdowns. Additionally, it covers the resilience strategies employed for Postgres and Redis to maintain service availability.
- [LangGraph Platform Plans Overview](https://langchain-ai.github.io/langgraph/concepts/plans/): This page provides an overview of the different plans available for the LangGraph Platform, including Developer, Plus, and Enterprise options. Each plan offers varying deployment options, usage limits, and features tailored to different user needs. For detailed pricing and related resources, links to additional documentation are also included.

# Examples

- [Building an Agentic RAG System](https://langchain-ai.github.io/langgraph/tutorials/rag/langgraph_agentic_rag/): This tutorial guides you through the process of creating a retrieval agent (RAG) system using LangChain and LangGraph. You will learn how to fetch and preprocess documents, create a retriever tool, and build an agentic RAG that intelligently decides when to retrieve information or respond directly to user queries. By the end, you'll have a functional system capable of semantic search and context-aware responses.
- [Building a Multi-Agent Supervisor System](https://langchain-ai.github.io/langgraph/tutorials/multi_agent/agent_supervisor/): This tutorial guides you through the process of creating a multi-agent supervisor system using specialized agents for research and math tasks. You will learn how to set up the environment, create individual worker agents, and implement a supervisor that orchestrates their interactions. By the end, you'll have a fully functional multi-agent architecture capable of handling complex queries.
- [Building a SQL Agent with LangChain](https://langchain-ai.github.io/langgraph/tutorials/sql-agent/): This tutorial provides a step-by-step guide on how to create a SQL agent capable of answering questions about a SQL database. It covers the setup of necessary dependencies, configuration of a SQLite database, and the implementation of a prebuilt agent that interacts with the database to generate and execute queries. Additionally, it discusses customizing the agent for more control over its behavior.
- [Custom Run ID, Tags, and Metadata for LangSmith Graph Runs](https://langchain-ai.github.io/langgraph/how-tos/run-id-langsmith/): This guide provides instructions on how to pass a custom run ID and set tags and metadata for graph runs in LangSmith. It covers prerequisites, configuration options, and includes code examples for setting up and running a graph with LangGraph. Additionally, it explains how to view and filter traces in the LangSmith platform.
- [Custom Authentication Setup for Chatbots](https://langchain-ai.github.io/langgraph/tutorials/auth/getting_started/): This tutorial guides you through the process of setting up custom authentication for a chatbot using the LangGraph platform. You will learn how to implement token-based security to control user access, starting with a basic example and preparing for more advanced authentication methods in future tutorials. By the end, you'll have a functional chatbot that restricts access to authenticated users.
- [Implementing Private Conversations in Chatbots](https://langchain-ai.github.io/langgraph/tutorials/auth/resource_auth/): This tutorial guides you through extending a chatbot to enable private conversations for each user by implementing resource-level access control. You'll learn how to add authorization handlers to ensure users can only access their own threads and test the functionality to confirm proper access restrictions. Additionally, the tutorial covers scoped authorization handlers for more granular control over resource access.
- [Integrating OAuth2 Authentication with Supabase](https://langchain-ai.github.io/langgraph/tutorials/auth/add_auth_server/): This tutorial guides you through replacing hard-coded tokens with real user accounts using OAuth2 for secure authentication in your LangGraph application. You'll learn how to set up Supabase as your identity provider, implement token validation, and ensure proper user authorization. By the end, you'll have a production-ready authentication system that allows users to securely access their own data.
- [Rebuilding Graphs at Runtime in LangGraph](https://langchain-ai.github.io/langgraph/cloud/deployment/graph_rebuild/): This guide explains how to rebuild your graph at runtime with different configurations in LangGraph. It covers the necessary prerequisites, how to define graphs, and the steps to modify your graph-making function for dynamic behavior based on user input. Additionally, it provides examples of both static and dynamic graph configurations.
- [Interacting with RemoteGraph in LangGraph](https://langchain-ai.github.io/langgraph/how-tos/use-remote-graph/): This documentation page provides a comprehensive guide on how to interact with a LangGraph Platform deployment using the RemoteGraph interface. It covers the initialization of RemoteGraph, invoking the graph both asynchronously and synchronously, and utilizing it as a subgraph. Additionally, it includes code examples in Python and JavaScript to facilitate understanding and implementation.
- [Deploying Agents on LangGraph Platform](https://langchain-ai.github.io/langgraph/how-tos/autogen-langgraph-platform/): This page provides a comprehensive guide on how to deploy agents like AutoGen and CrewAI using the LangGraph Platform. It covers the necessary setup, agent definition, and wrapping the agent in a LangGraph node for deployment. Additionally, it highlights the benefits of using LangGraph for scalable infrastructure and memory support.
- [Integrating LangGraph with React: A Comprehensive Guide](https://langchain-ai.github.io/langgraph/cloud/how-tos/use_stream_react/): This documentation provides a detailed guide on how to integrate the LangGraph platform into your React applications using the `useStream()` hook. It covers installation, key features, example implementations, and customization options for building chat experiences. Additionally, it includes advanced topics such as event handling, TypeScript support, and managing conversation threads.
- [Implementing Generative User Interfaces with LangGraph](https://langchain-ai.github.io/langgraph/cloud/how-tos/generative_ui_react/): This documentation provides a comprehensive guide on how to implement Generative User Interfaces (Generative UI) using the LangGraph platform. It covers prerequisites, step-by-step tutorials for defining UI components, sending them in graphs, and handling them in React applications. Additionally, it includes how-to guides for customizing components and managing UI state effectively.

# Resources

- [LangGraph FAQ](https://langchain-ai.github.io/langgraph/concepts/faq/): This FAQ page provides answers to common questions about LangGraph, an orchestration framework for complex agentic systems. It covers topics such as the differences between LangGraph and LangChain, performance impacts, open-source status, and compatibility with various LLMs. Additionally, it outlines the distinctions between LangGraph and LangGraph Platform, including features and deployment options.
- [Getting Started with LangGraph Templates](https://langchain-ai.github.io/langgraph/concepts/template_applications/): This page provides an overview of open source reference applications known as templates, designed to help users quickly build applications with LangGraph. It includes installation instructions for the LangGraph CLI, a list of available templates with their descriptions, and guidance on creating and deploying a new LangGraph app. Users can find links to repositories for each template and next steps for customizing their applications.
- [Guide to Using llms.txt and llms-full.txt for LLMs](https://langchain-ai.github.io/langgraph/llms-txt-overview/): This page provides an overview of the `llms.txt` and `llms-full.txt` formats, which facilitate access to programming documentation for large language models (LLMs) and agents. It outlines the differences between the two formats, usage instructions via an MCP server, and best practices for integrating these files into integrated development environments (IDEs). Additionally, it highlights considerations for managing large documentation files effectively.
- [Community Agents for LangGraph](https://langchain-ai.github.io/langgraph/agents/prebuilt/): This page provides a list of community-built libraries that extend the functionality of LangGraph. Each entry includes the library name, GitHub URL, a brief description, and additional metrics like weekly downloads and stars. Additionally, it outlines how to contribute your own library to the LangGraph documentation.
- [LangGraph Error Reference Guide](https://langchain-ai.github.io/langgraph/troubleshooting/errors/index/): This page serves as a comprehensive reference for resolving common errors encountered while using the LangGraph platform. It includes a list of error codes and links to detailed guides for troubleshooting specific issues. Users can find solutions for errors related to graph recursion, concurrent updates, node return values, and more.
- [Handling Recursion Limits in LangGraph](https://langchain-ai.github.io/langgraph/troubleshooting/errors/GRAPH_RECURSION_LIMIT/): This page provides guidance on managing recursion limits in LangGraph's StateGraph. It explains how to identify potential infinite loops in your graph and offers solutions for increasing the recursion limit when working with complex graphs. Additionally, it includes code examples to illustrate the concepts discussed.
- [Handling INVALID_CONCURRENT_GRAPH_UPDATE in LangGraph](https://langchain-ai.github.io/langgraph/troubleshooting/errors/INVALID_CONCURRENT_GRAPH_UPDATE/): This page explains the INVALID_CONCURRENT_GRAPH_UPDATE error that occurs in LangGraph when multiple nodes attempt to update the same state property concurrently. It provides an example of how this error can arise and offers a solution by using a reducer to combine values from parallel node executions. Additionally, troubleshooting tips are included to help resolve this issue.
- [Handling Invalid Node Return Values in LangGraph](https://langchain-ai.github.io/langgraph/troubleshooting/errors/INVALID_GRAPH_NODE_RETURN_VALUE/): This page provides guidance on the error encountered when a LangGraph node returns a non-dict value. It includes an example of incorrect node implementation and the resulting error message. Additionally, troubleshooting tips are offered to ensure that all nodes return the expected dictionary format.
- [Handling Multiple Subgraphs in LangGraph](https://langchain-ai.github.io/langgraph/troubleshooting/errors/MULTIPLE_SUBGRAPHS/): This page discusses the limitations of calling multiple subgraphs within a single LangGraph node when checkpointing is enabled. It provides troubleshooting tips to resolve related errors, including suggestions for compiling subgraphs without checkpointing and using the Send API for graph calls.
- [Handling INVALID_CHAT_HISTORY Error in create_react_agent](https://langchain-ai.github.io/langgraph/troubleshooting/errors/INVALID_CHAT_HISTORY/): This page provides an overview of the INVALID_CHAT_HISTORY error encountered in the create_react_agent function when a malformed list of messages is passed. It outlines the potential causes of the error and offers troubleshooting steps to resolve it. Users can learn how to properly invoke the graph and manage tool calls to avoid this issue.
- [Handling INVALID_LICENSE Error in LangGraph Platform](https://langchain-ai.github.io/langgraph/troubleshooting/errors/INVALID_LICENSE/): This page provides guidance on troubleshooting the INVALID_LICENSE error encountered when starting a self-hosted LangGraph Platform server. It outlines the scenarios in which this error may occur and offers solutions based on different deployment types. Additionally, it includes steps to verify the necessary credentials for successful deployment.
- [LangGraph Studio Troubleshooting Guide](https://langchain-ai.github.io/langgraph/troubleshooting/studio/): This page provides troubleshooting solutions for common connection issues encountered in LangGraph Studio, particularly with Safari and Brave browsers. It also addresses potential graph edge issues and offers methods to define routing paths for conditional edges. Users can find step-by-step instructions for resolving these issues using Cloudflare Tunnel and browser settings.
- [LangGraph Case Studies](https://langchain-ai.github.io/langgraph/adopters/): This page provides a comprehensive list of companies that have successfully implemented LangGraph, showcasing their unique use cases and the benefits they have achieved. Each entry includes links to detailed case studies or blog posts for further reading. If your company uses LangGraph, you are encouraged to share your success story to contribute to this growing collection.
解释