"""
K8s SRE 多智能体系统主程序
简化版本 - 按照开发指南实现
"""

import uuid
from typing import Literal
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import InMemorySaver

from src.state import SREMultiAgentState
from src.agents.supervisor import sre_supervisor_agent, members
from src.agents.specialist_agents import (
    k8s_diagnostic_agent,
    sls_diagnostic_agent,
    arms_diagnostic_agent,
    k8s_operations_agent
)


def human_in_the_loop(state: SREMultiAgentState):
    """人机交互节点 - 需要人工确认时暂停"""
    print("\n--- 需要人工确认 ---")
    print("主管建议:", state['messages'][-1].content)
    # 简化：直接返回，在主循环中处理确认
    return state


def should_continue(state: SREMultiAgentState) -> Literal["supervisor", "human_in_the_loop"]:
    """决定下一步流程"""
    if not state["messages"]:
        return "supervisor"

    last_message = state["messages"][-1]
    content = last_message.content.lower() if hasattr(last_message, 'content') else ""

    # 如果涉及操作执行，需要人工确认
    if "k8s_operations_agent" in content and ("执行" in content or "操作" in content):
        return "human_in_the_loop"

    return "supervisor"


# 构建图
builder = StateGraph(SREMultiAgentState)

# 添加节点
builder.add_node("supervisor", sre_supervisor_agent)
builder.add_node("human_in_the_loop", human_in_the_loop)

# 添加专家节点
for member in members:
    if member == "k8s_diagnostic_agent":
        builder.add_node(member, k8s_diagnostic_agent)
    elif member == "sls_diagnostic_agent":
        builder.add_node(member, sls_diagnostic_agent)
    elif member == "arms_diagnostic_agent":
        builder.add_node(member, arms_diagnostic_agent)
    elif member == "k8s_operations_agent":
        builder.add_node(member, k8s_operations_agent)

# 添加边
builder.add_conditional_edges("supervisor", should_continue)
builder.add_edge("human_in_the_loop", "supervisor")

# 专家完成后返回主管
for member in members:
    builder.add_edge(member, "supervisor")

# 设置入口点
builder.add_edge(START, "supervisor")

# 编译图
graph = builder.compile(checkpointer=InMemorySaver())


def run_graph(input_message: str):
    """运行多智能体系统"""
    initial_state = {
        "messages": [("user", input_message)],
        "task": input_message,
        "supervisor_report": "",
        "last_active_agent": None,
        "current_task_status": "pending"
    }

    print(f"🚀 启动 K8s SRE 多智能体系统")
    print(f"📝 任务: {input_message}")
    print("=" * 50)

    try:
        # 简化：直接调用主管
        result = sre_supervisor_agent(initial_state)
        print(f"\n✅ 主管分析结果:")
        print(result["messages"][-1].content)
    except Exception as e:
        print(f"❌ 系统错误: {e}")


if __name__ == "__main__":
    # 示例运行
    test_message = "集群中有 Pod 出现 OOMKilled，请帮忙排查和解决"
    run_graph(test_message)
