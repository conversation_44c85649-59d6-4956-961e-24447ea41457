[project]
name = "k8s-sre-agent"
version = "0.1.0"
description = "基于 LangGraph 的 K8s SRE 智能助手 - 支持 OOM 问题排查和集群健康巡检"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "langgraph>=0.2.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.0.0",
    "tenacity>=8.0.0",
    "asyncio-mqtt>=0.16.0",
    "httpx>=0.25.0",
    "python-dotenv>=1.0.0",
    "langchain-mcp-adapters>=0.1.0",
    "mcp>=1.9.0",
    "fastmcp>=2.10.6",
    # <PERSON><PERSON><PERSON><PERSON> 集成
    "langchain-community>=0.2.0",
    "langchain-core>=0.2.0",
    # OpenAI SDK 兼容
    "openai>=1.0.0",
    # Qwen/DashScope 支持
    "dashscope>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
