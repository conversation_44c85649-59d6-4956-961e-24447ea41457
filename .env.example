# K8s SRE 智能助手环境变量配置示例
# 阿里云百炼 API 密钥 (当 USE_QWEN_LLM=true 时必需)
# 获取方式：https://bailian.console.aliyun.com/#/api-key
DASHSCOPE_API_KEY=sk-your-dashscope-api-key-here

# Qwen 模型名称 (可选，默认为 qwen-plus)
# 可选值：qwen-plus, qwen-max, qwen-turbo 等
QWEN_MODEL=qwen-plus

# Qwen 实现方式 (可选，系统会自动选择最佳方式)
# 优先级: langchain > openai_sdk > direct_http
# langchain: 使用 LangChain ChatTongyi 集成 (推荐)
# openai_sdk: 使用 OpenAI SDK 兼容模式
# direct_http: 直接 HTTP 调用 (回退方案)
# QWEN_IMPLEMENTATION=langchain

# =============================================================================
# MCP 工具配置
# =============================================================================

# MCP 服务器 URL (当 USE_MCP_TOOLS=true 时必需)
# 支持 SSE 协议的 HTTP 端点
MCP_SERVER_URL=http://localhost:8000/mcp/
# MCP_K8S_SERVER_URL=http://localhost:8001/mcp/
# MCP_SLS_SERVER_URL=http://localhost:8002/mcp/
# MCP_ARMS_SERVER_URL=http://localhost:8003/mcp/

# MCP 服务器认证令牌 (可选)
MCP_SERVER_AUTH_TOKEN=your-auth-token-here

# =============================================================================
# 应用配置
# ========================================================================