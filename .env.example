# K8s SRE 智能助手环境变量配置示例
# 复制此文件为 .env 并填入实际值

# =============================================================================
# LLM 配置
# =============================================================================

# 是否使用 Qwen LLM (true/false)
USE_QWEN_LLM=false

# 阿里云百炼 API 密钥 (当 USE_QWEN_LLM=true 时必需)
# 获取方式：https://bailian.console.aliyun.com/#/api-key
DASHSCOPE_API_KEY=sk-your-dashscope-api-key-here

# Qwen 模型名称 (可选，默认为 qwen-plus)
# 可选值：qwen-plus, qwen-max, qwen-turbo 等
QWEN_MODEL=qwen-plus

# Qwen 实现方式 (可选，系统会自动选择最佳方式)
# 优先级: langchain > openai_sdk > direct_http
# langchain: 使用 LangChain ChatTongyi 集成 (推荐)
# openai_sdk: 使用 OpenAI SDK 兼容模式
# direct_http: 直接 HTTP 调用 (回退方案)
# QWEN_IMPLEMENTATION=langchain

# =============================================================================
# MCP 工具配置
# =============================================================================

# 是否使用 MCP 工具 (true/false)
USE_MCP_TOOLS=false

# MCP 服务器 URL (当 USE_MCP_TOOLS=true 时必需)
# 支持 SSE 协议的 HTTP 端点
MCP_SERVER_URL=http://localhost:8000/mcp/

# MCP 服务器认证令牌 (可选)
MCP_SERVER_AUTH_TOKEN=your-auth-token-here

# =============================================================================
# 应用配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 是否启用详细日志
VERBOSE_LOGGING=false

# =============================================================================
# 示例 MCP 服务器配置
# =============================================================================

# 如果你有多个 MCP 服务器，可以通过以下方式配置：
# MCP_K8S_SERVER_URL=http://localhost:8001/mcp/
# MCP_SLS_SERVER_URL=http://localhost:8002/mcp/
# MCP_ARMS_SERVER_URL=http://localhost:8003/mcp/

# =============================================================================
# 开发配置
# =============================================================================

# 开发模式 (true/false)
DEV_MODE=false

# 是否跳过 SSL 验证 (仅开发环境)
SKIP_SSL_VERIFY=false
